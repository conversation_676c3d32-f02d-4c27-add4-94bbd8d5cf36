import Image from "next/image";

export interface ProfileBioProps {
  name: string;
  role: string;
  imageUrl: string;
  imageAlt: string;
}

const ProfileBio = ({ name, role, imageUrl, imageAlt }: ProfileBioProps) => {
  return (
    <div>
        <div className="w-12 h-12 rounded-full">
            <Image
                src={imageUrl}
                alt={imageAlt}
                fill
                className="object-cover w-12 h-12"
            />
        </div>
        {/* Name and Role */}
        <div className="flex flex-col">
            <p className="text-default-900 font-semibold text-sm">{name}</p>
            <p className="text-default-600 text-xs">{role}</p>
        </div>
    </div>
  );
};

export default ProfileBio;