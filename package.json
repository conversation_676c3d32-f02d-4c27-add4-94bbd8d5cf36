{"name": "gro-turbo-repo", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "start": "turbo run start", "dev:dashboard": "turbo run dev --filter=cms-dashboard", "dev:api": "turbo run dev --filter=api", "dev:frontend": "turbo run dev --filter=frontend", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "clean": "sh ./scripts/npm-clean-install.sh"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.4", "prettier": "^3.5.0", "tailwindcss": "^4.1.8", "turbo": "^2.4.2", "typescript": "^5.0.0"}, "engines": {"node": ">=18"}, "packageManager": "npm@10.1.0", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "react": "^18.3.1", "react-dom": "^18.3.1", "zod": "^3.25.67"}}