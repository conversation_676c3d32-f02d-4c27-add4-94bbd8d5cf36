# This GitHub Actions workflow runs on pull requests to the 'Development' branch.
# It performs lint checks, type checks, and builds all workspaces using Turborepo.
name: CI Checks

on:
  pull_request:
    branches:
      - Development

jobs:
  # Job 1: Run Lint Checks
  lint:
    name: Lint Checks
    runs-on: ubuntu-latest 

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4 # Action to check out your repository code

      - name: Setup Node.js
        uses: actions/setup-node@v4 # Action to set up Node.js environment
        with:
          node-version: '18' # Use Node.js version 18 or '20' if that's your engine target
          cache: 'npm'       # Cache npm dependencies to speed up subsequent runs

      - name: Cache Turborepo build cache
        uses: actions/cache@v4 # Cache Turborepo's internal build cache
        with:
          path: .turbo
          # Key is unique per commit, restore-keys tries to find a cache from previous runs
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Install Dependencies
        run: npm install # Install all root and workspace dependencies

      - name: Run Lint Checks (turbo run lint)
        run: npm run lint # Execute your Turborepo lint command

  # Job 2: Run Type Checks
  typecheck:
    name: Type Checks
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Cache Turborepo build cache
        uses: actions/cache@v4
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Install Dependencies
        run: npm install

      - name: Run Type Checks (turbo run check-types)
        run: npm run check-types # Execute your Turborepo type check command

  # Job 3: Dependency Checks
  # This job runs dependency audits to check for vulnerabilities in the dependencies.
  dependency-audit:
    name: Dependency Audit
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          #cache: 'npm'

      # Cache step for Turborepo build cache removed for now

      - name: Install Dependencies
        run: npm install

      - name: Run npm audit for vulnerabilities
        run: npm audit --audit-level=moderate

  # Job 4: Build Frontend Only
  build:
    name: Build Frontend Only
    runs-on: ubuntu-latest
    needs: [lint, typecheck, dependency-audit]

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          #cache: 'npm'

      # Cache step for Turborepo build cache removed for now
      # - name: Cache Turborepo build cache
      #   uses: actions/cache@v4
      #   with:
      #     path: .turbo
      #     key: ${{ runner.os }}-turbo-${{ github.sha }}
      #     restore-keys: |
      #       ${{ runner.os }}-turbo-

      - name: Install Dependencies
        run: npm install

      - name: Run Build (Frontend Only)
        # run: npm run build // for all workspaces
        run: npm run build --filter=frontend

