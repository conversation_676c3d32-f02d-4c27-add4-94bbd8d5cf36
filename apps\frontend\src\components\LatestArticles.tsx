import React from "react";
import ArticleCard, { ArticleCardProps } from "./ui/ArticleCard";

const LatestArticles = () => {
    const articles: ArticleCardProps[] = [
        {
            id: 1,
            title: "Starting a relationship with God is more than just prayer",
            description: "What does it mean to follow God? What does it mean to pray. " + 
                            "Learn practical steps for spiritual growth.",
            date: "2025-02-18",
            imageUrl: "/images/hands-image.png",
            imageAlt: "Hands folded for prayer",
            author: "<PERSON>",
            authorImage: "/images/Fred-Profile-Pic.png",
            authorImageAlt: "<PERSON>'s profile picture",
            authorRole: "Content Creator",
          },
          {
            id: 2,
            title: "What is repentance?",
            description: "Repentance in the Bible is a heartfelt recognition of sin, " + 
                            "turning away from that sin, and a reorientation toward God.",
            date: "2025-02-18",
            imageUrl: "/images/people.png",
            imageAlt: "Two guys sitted by a table",
            author: "<PERSON>",
            authorImage: "/images/John-profile-pic.png",
            authorImageAlt: "<PERSON>'s profile picture",
            authorRole: "Website Administrator",
          },
          {
            id: 3,
            title: "Sunday Worship",
            description: "Worshipping God is much more than attending church and " + 
                            "engaging in worship on Sundays.",
            date: "2025-02-18",
            imageUrl: "/images/crowd-hands.png",
            imageAlt: "People worshiping in a crowd",
            author: "Bobby Bob",
            authorRole: "Website Administrator",
          },
        ];

    return (
        <div className="px-28 py-16 flex justify-center">
            <div className="flex flex-col gap-10">
                <h3 className="self-stretch justify-start text-default-900 text-5xl font-semibold font-['Poppins'] leading-[60px]">
                    Latest Articles
                </h3>
                <div className="flex flex-wrap justify-start gap-6">
                    {articles.map((article) => (
                        <ArticleCard key={article.id} {...article} />
                    ))}
                </div>
            </div>
        </div>
    )
}

export default LatestArticles;