"use client"

import { motion } from "framer-motion";
import Image from "next/image";
import ProfileBio from "./ProfileBio";

export interface ArticleCardProps {
    id: number;
    imageUrl: string;
    imageAlt: string;
    date: string;
    title: string;
    description: string;
    author: string;
    authorImage?: string;
    authorImageAlt?: string;
    authorRole: string;
}

const ArticleCard = ({ imageUrl, imageAlt, date, title, description, 
                        author, authorImage, authorImageAlt, authorRole }: ArticleCardProps) => {
    const formattedDate = new Date(date).toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
    });
    const defaultImageUrl = "/images/default-pic.png";

    return (
        <div className="flex flex-col justify-start items-start w-[389.33px] gap-4 bg-white rounded-xl p-2 overflow-hidden">
            {/* Image that shrinks vertically on hover */}
            <motion.div
                initial={{ height: 265 }}
                animate={{ height: 300 }}
                transition={{ duration: 0.3 }}
                className="relative lg:w-[389.33px] rounded-xl overflow-hidden"
            >
                <Image
                    src={imageUrl}
                    alt={imageAlt}
                    fill
                    className="object-cover rounded-xl transition-all duration-300"
                />
            </motion.div>
            {/* Text */}
            <div className="flex flex-col gap-2">
                <p className="text-sm text-gray-500">
                    {formattedDate}
                </p>
                <h3 className="font-semibold text-lg text-default-900">
                    {title}
                </h3>
                <p className="text-sm text-default-600">
                    {description}
                </p>
            </div>
            <div>
                <ProfileBio
                    name={author}
                    role={authorRole}
                    imageUrl={authorImage || defaultImageUrl}
                    imageAlt={authorImageAlt || "Default Profile Icon"}
                />
            </div>
        </div>
    );

}

export default ArticleCard;

// "use client";

// import { motion } from "framer-motion";
// import Image from "next/image";
// import { useState } from "react";
// import SecondaryBtn from "./SecondaryBtn";

// interface FollowGodProps {
//     title: string;
//     description: string;
//     imageUrl: string;
//     imageAlt: string;
// }

// const FollowGodCard = ({ title, description, imageUrl, imageAlt }: FollowGodProps) => {
//     const [isHovered, setIsHovered] = useState(false);

//     return (
//         <motion.div
//             onHoverStart={() => setIsHovered(true)}
//             onHoverEnd={() => setIsHovered(false)}
//             initial={{ height: 460 }}
//             animate={{ height: isHovered ? 460 : 460 }} // static height
//             transition={{ duration: 0.3 }}
//             className="flex flex-col justify-start items-start lg:w-[384px] md:w-[208px] gap-4 bg-white rounded-xl shadow-lg p-2 transition hover:shadow-2xl overflow-hidden"
//         >
//             {/* Image that shrinks vertically on hover */}
//             <motion.div
//                 initial={{ height: 265 }}
//                 animate={{ height: isHovered ? 220 : 265 }}
//                 transition={{ duration: 0.3 }}
//                 className="relative lg:w-[368px] rounded-xl overflow-hidden"
//             >
//                 <Image
//                     src={imageUrl}
//                     alt={imageAlt}
//                     fill
//                     className="object-cover rounded-xl transition-all duration-300"
//                 />
//             </motion.div>

//             {/* Text that moves up slightly */}
//             <motion.div
//                 animate={{ y: isHovered ? -10 : 0 }}
//                 transition={{ duration: 0.3 }}
//                 className="relative flex flex-col justify-start items-start gap-2 p-5"
//             >
//                 <h3 className="text-default-900 font-semibold text-base md:text-lg font-poppins">
//                     {title}
//                 </h3>
//                 <p className="text-default-600 text-sm font-normal font-poppins">
//                     {description}
//                 </p>
//             </motion.div>

//             {/* Reserved space for the button – hidden unless hovered */}
//             <div className="px-5 pb-4 h-[40px]">
//                 <motion.div
//                     initial={{ opacity: 0, y: 10 }}
//                     animate={{ opacity: isHovered ? 1 : 0, y: isHovered ? 0 : 10 }}
//                     transition={{ duration: 0.3 }}
//                     className="w-fit"
//                 >
//                     <div className="justify-start text-colors-base-secondary text-sm font-semibold font-['Poppins'] leading-tight">
//                             <SecondaryBtn text="Button" />
//                     </div>
//                 </motion.div>
//             </div>
//         </motion.div>
//     );
// };

// export default FollowGodCard;
